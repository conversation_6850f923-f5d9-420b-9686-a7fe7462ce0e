//
//  AIStrategy.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import Foundation

/// AI决策类型
enum AIDecision {
    case play(CardCombination)  // 出牌
    case pass                   // 过牌
}

/// AI策略类 - 实现简单的贪心策略
class AIStrategy {
    
    /// AI决策主入口
    func makeDecision(player: Player, gameState: GameState) -> AIDecision {
        guard player.type == .ai else { return .pass }
        
        // 获取所有可能的出牌组合
        let validCombinations = getValidCombinations(for: player, gameState: gameState)
        
        if validCombinations.isEmpty {
            return .pass
        }
        
        // 根据游戏阶段选择策略
        let strategy = selectStrategy(gameState: gameState, player: player)
        
        switch strategy {
        case .aggressive:
            return makeAggressiveDecision(validCombinations, gameState: gameState)
        case .conservative:
            return makeConservativeDecision(validCombinations, gameState: gameState)
        case .balanced:
            return makeBalancedDecision(validCombinations, gameState: gameState)
        case .endGame:
            return makeEndGameDecision(validCombinations, gameState: gameState)
        }
    }
    
    /// 获取有效出牌组合
    private func getValidCombinations(for player: Player, gameState: GameState) -> [CardCombination] {
        let ruleValidator = RuleValidator()
        return ruleValidator.getValidCombinations(for: player, gameState: gameState)
    }
    
    /// 选择AI策略
    private func selectStrategy(gameState: GameState, player: Player) -> AIStrategyType {
        let cardCount = player.cardCount
        let totalCards = gameState.config.cardsPerPlayer
        
        // 根据剩余手牌数量选择策略
        if cardCount <= 3 {
            return .endGame      // 残局阶段，激进出牌
        } else if cardCount <= totalCards / 3 {
            return .aggressive   // 中后期，积极出牌
        } else if cardCount <= totalCards * 2 / 3 {
            return .balanced     // 中期，平衡策略
        } else {
            return .conservative // 前期，保守策略
        }
    }
    
    /// 激进策略 - 优先出大牌
    private func makeAggressiveDecision(_ combinations: [CardCombination], gameState: GameState) -> AIDecision {
        // 优先出炸弹
        if let bomb = combinations.first(where: { $0.type == .bomb }) {
            return .play(bomb)
        }
        
        // 优先出大的牌型
        let sortedByStrength = combinations.sorted { 
            calculateCombinationStrength($0) > calculateCombinationStrength($1) 
        }
        
        if let bestCombination = sortedByStrength.first {
            return .play(bestCombination)
        }
        
        return .pass
    }
    
    /// 保守策略 - 优先出小牌
    private func makeConservativeDecision(_ combinations: [CardCombination], gameState: GameState) -> AIDecision {
        // 避免出炸弹，除非必要
        let nonBombCombinations = combinations.filter { $0.type != .bomb }
        
        if !nonBombCombinations.isEmpty {
            // 出最小的牌型
            let sortedByStrength = nonBombCombinations.sorted { 
                calculateCombinationStrength($0) < calculateCombinationStrength($1) 
            }
            
            if let smallestCombination = sortedByStrength.first {
                return .play(smallestCombination)
            }
        }
        
        // 如果只有炸弹可出，考虑过牌
        if combinations.allSatisfy({ $0.type == .bomb }) {
            // 50%概率过牌
            if Bool.random() {
                return .pass
            }
        }
        
        if let combination = combinations.first {
            return .play(combination)
        }
        
        return .pass
    }
    
    /// 平衡策略 - 综合考虑
    private func makeBalancedDecision(_ combinations: [CardCombination], gameState: GameState) -> AIDecision {
        // 分析当前局势
        let gameAnalysis = analyzeGameSituation(gameState: gameState)
        
        // 根据局势调整策略
        if gameAnalysis.shouldBeAggressive {
            return makeAggressiveDecision(combinations, gameState: gameState)
        } else if gameAnalysis.shouldBeConservative {
            return makeConservativeDecision(combinations, gameState: gameState)
        }
        
        // 默认选择中等强度的牌型
        let sortedCombinations = combinations.sorted { 
            calculateCombinationStrength($0) < calculateCombinationStrength($1) 
        }
        
        let midIndex = sortedCombinations.count / 2
        if midIndex < sortedCombinations.count {
            return .play(sortedCombinations[midIndex])
        }
        
        return .pass
    }
    
    /// 残局策略 - 尽快出完牌
    private func makeEndGameDecision(_ combinations: [CardCombination], gameState: GameState) -> AIDecision {
        // 优先出能清空最多牌的组合
        let sortedByCardCount = combinations.sorted { 
            $0.cards.count > $1.cards.count 
        }
        
        if let bestCombination = sortedByCardCount.first {
            return .play(bestCombination)
        }
        
        return .pass
    }
    
    /// 计算牌型强度
    private func calculateCombinationStrength(_ combination: CardCombination) -> Int {
        let baseStrength = combination.type.weight * 100
        let rankStrength = combination.mainRank?.weight ?? 0
        let cardCountBonus = combination.cards.count * 5
        
        return baseStrength + rankStrength + cardCountBonus
    }
    
    /// 分析游戏局势
    private func analyzeGameSituation(gameState: GameState) -> GameAnalysis {
        let players = gameState.players
        let currentPlayerIndex = gameState.currentPlayerIndex
        
        // 计算其他玩家的平均手牌数
        let otherPlayers = players.enumerated().compactMap { index, player in
            index != currentPlayerIndex ? player : nil
        }
        
        let averageCardCount = otherPlayers.isEmpty ? 0 : 
            otherPlayers.map { $0.cardCount }.reduce(0, +) / otherPlayers.count
        
        let currentPlayerCardCount = players[currentPlayerIndex].cardCount
        
        // 判断是否应该激进
        let shouldBeAggressive = currentPlayerCardCount > averageCardCount + 3
        
        // 判断是否应该保守
        let shouldBeConservative = currentPlayerCardCount < averageCardCount - 2
        
        return GameAnalysis(
            shouldBeAggressive: shouldBeAggressive,
            shouldBeConservative: shouldBeConservative,
            averageOpponentCards: averageCardCount,
            currentPlayerCards: currentPlayerCardCount
        )
    }
    
    /// 评估出牌价值
    private func evaluatePlayValue(_ combination: CardCombination, gameState: GameState) -> Double {
        var value: Double = 0
        
        // 基础价值：出牌数量
        value += Double(combination.cards.count) * 10
        
        // 牌型价值
        switch combination.type {
        case .single:
            value += 5
        case .pair:
            value += 15
        case .triple:
            value += 25
        case .straight:
            value += 30
        case .pairStraight:
            value += 40
        case .tripleStraight:
            value += 50
        case .bomb:
            value += 100  // 炸弹价值很高，但要谨慎使用
        case .invalid:
            value = 0
        }
        
        // 点数价值（小牌价值更高，因为容易出手）
        if let mainRank = combination.mainRank {
            value += Double(15 - mainRank.weight) * 2
        }
        
        // 如果是最后几张牌，价值翻倍
        let currentPlayer = gameState.players[gameState.currentPlayerIndex]
        if currentPlayer.cardCount <= 5 {
            value *= 2
        }
        
        return value
    }
}

// MARK: - 辅助枚举和结构体

/// AI策略类型
enum AIStrategyType {
    case aggressive     // 激进策略
    case conservative   // 保守策略
    case balanced       // 平衡策略
    case endGame        // 残局策略
}

/// 游戏局势分析结果
struct GameAnalysis {
    let shouldBeAggressive: Bool
    let shouldBeConservative: Bool
    let averageOpponentCards: Int
    let currentPlayerCards: Int
}

// MARK: - AI难度扩展
extension AIStrategy {
    
    /// 根据难度调整AI决策
    func makeDecisionWithDifficulty(
        player: Player, 
        gameState: GameState, 
        difficulty: AIDifficulty
    ) -> AIDecision {
        let baseDecision = makeDecision(player: player, gameState: gameState)
        
        switch difficulty {
        case .easy:
            return adjustForEasyDifficulty(baseDecision, player: player, gameState: gameState)
        case .normal:
            return baseDecision
        case .hard:
            return adjustForHardDifficulty(baseDecision, player: player, gameState: gameState)
        }
    }
    
    /// 简单难度调整（偶尔做出次优决策）
    private func adjustForEasyDifficulty(_ decision: AIDecision, player: Player, gameState: GameState) -> AIDecision {
        // 20%概率做出随机决策
        if Double.random(in: 0...1) < 0.2 {
            let validCombinations = getValidCombinations(for: player, gameState: gameState)
            if !validCombinations.isEmpty && Bool.random() {
                return .play(validCombinations.randomElement()!)
            }
        }
        
        return decision
    }
    
    /// 困难难度调整（更优化的决策）
    private func adjustForHardDifficulty(_ decision: AIDecision, player: Player, gameState: GameState) -> AIDecision {
        // 在困难模式下，AI会考虑更多因素
        // 例如：记忆其他玩家出过的牌，预测对手手牌等
        // 这里简化实现，主要是更精确的价值评估
        
        let validCombinations = getValidCombinations(for: player, gameState: gameState)
        
        if !validCombinations.isEmpty {
            // 使用更复杂的评估函数
            let bestCombination = validCombinations.max { 
                evaluatePlayValue($0, gameState: gameState) < evaluatePlayValue($1, gameState: gameState) 
            }
            
            if let best = bestCombination {
                return .play(best)
            }
        }
        
        return decision
    }
}

/// AI难度枚举
enum AIDifficulty: String, CaseIterable {
    case easy = "简单"
    case normal = "普通"
    case hard = "困难"
}
