//
//  GameEngine.swift
//  pdk
//
//  Created by <PERSON>e<PERSON><PERSON> on 2025/8/21.
//

import Foundation
import Combine

/// 游戏引擎 - 负责游戏流程控制和状态管理
class GameEngine: ObservableObject {
    
    // MARK: - 发布属性
    @Published var gameState: GameState
    @Published var isProcessing: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - 私有属性
    private var gameTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    private let ruleValidator = RuleValidator()
    private let aiStrategy = AIStrategy()
    
    // MARK: - 初始化
    init(gameMode: GameMode = .single, config: GameConfig = .default) {
        self.gameState = GameState(mode: gameMode, config: config)
        setupGame()
    }
    
    // MARK: - 游戏设置
    private func setupGame() {
        // 监听游戏状态变化
        $gameState
            .sink { [weak self] state in
                self?.handleGameStateChange(state)
            }
            .store(in: &cancellables)
    }
    
    /// 处理游戏状态变化
    private func handleGameStateChange(_ state: GameState) {
        switch state.phase {
        case .playing:
            startTurnTimer()
        case .finished:
            stopTurnTimer()
        default:
            break
        }
    }
    
    // MARK: - 游戏控制
    
    /// 创建新游戏
    func createNewGame(mode: GameMode = .single, config: GameConfig = .default) {
        gameState = GameState(mode: mode, config: config)
        errorMessage = nil
        
        // 添加AI玩家（单机模式）
        if mode == .single {
            addAIPlayers()
        }
    }
    
    /// 添加AI玩家
    private func addAIPlayers() {
        // 添加人类玩家
        let humanPlayer = Player(
            name: "玩家",
            type: .human,
            position: 0
        )
        gameState.addPlayer(humanPlayer)
        
        // 添加两个AI玩家
        let ai1 = Player(
            name: "AI-小明",
            type: .ai,
            position: 1,
            avatar: "ai_avatar_1"
        )
        gameState.addPlayer(ai1)
        
        let ai2 = Player(
            name: "AI-小红",
            type: .ai,
            position: 2,
            avatar: "ai_avatar_2"
        )
        gameState.addPlayer(ai2)
    }
    
    /// 开始游戏
    func startGame() {
        guard gameState.phase == .ready else {
            setError("游戏状态不正确，无法开始")
            return
        }
        
        isProcessing = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.gameState.startGame()
            self?.isProcessing = false
            
            // 如果第一个玩家是AI，自动出牌
            self?.checkAITurn()
        }
    }
    
    /// 玩家出牌
    func playCards(_ cards: [Card]) -> Bool {
        guard let currentPlayer = gameState.currentPlayer,
              currentPlayer.type == .human else {
            setError("不是您的回合")
            return false
        }
        
        let combination = CardCombination(cards: cards)
        
        // 验证出牌规则
        if !ruleValidator.validatePlay(combination, gameState: gameState) {
            setError("出牌不符合规则")
            return false
        }
        
        // 执行出牌
        let success = gameState.playCards(combination, playerId: currentPlayer.id)
        
        if success {
            clearError()
            // 检查下一个玩家是否为AI
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                self?.checkAITurn()
            }
        } else {
            setError("出牌失败")
        }
        
        return success
    }
    
    /// 玩家过牌
    func passCards() -> Bool {
        guard let currentPlayer = gameState.currentPlayer,
              currentPlayer.type == .human else {
            setError("不是您的回合")
            return false
        }
        
        // 验证是否可以过牌
        if !ruleValidator.canPass(gameState: gameState) {
            setError("当前情况下不能过牌")
            return false
        }
        
        let success = gameState.passCards(playerId: currentPlayer.id)
        
        if success {
            clearError()
            // 检查下一个玩家是否为AI
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                self?.checkAITurn()
            }
        } else {
            setError("过牌失败")
        }
        
        return success
    }
    
    /// 检查AI回合
    private func checkAITurn() {
        guard let currentPlayer = gameState.currentPlayer,
              currentPlayer.type == .ai,
              gameState.phase == .playing else {
            return
        }
        
        // AI思考时间
        DispatchQueue.main.asyncAfter(deadline: .now() + Double.random(in: 1.0...3.0)) { [weak self] in
            self?.executeAITurn()
        }
    }
    
    /// 执行AI回合
    private func executeAITurn() {
        guard let currentPlayer = gameState.currentPlayer,
              currentPlayer.type == .ai else {
            return
        }
        
        // AI决策
        let decision = aiStrategy.makeDecision(
            player: currentPlayer,
            gameState: gameState
        )
        
        switch decision {
        case .play(let combination):
            let success = gameState.playCards(combination, playerId: currentPlayer.id)
            if success {
                // 继续检查下一个玩家
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                    self?.checkAITurn()
                }
            }
            
        case .pass:
            let success = gameState.passCards(playerId: currentPlayer.id)
            if success {
                // 继续检查下一个玩家
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                    self?.checkAITurn()
                }
            }
        }
    }
    
    // MARK: - 计时器管理
    
    /// 开始回合计时器
    private func startTurnTimer() {
        stopTurnTimer()
        
        gameTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimer()
        }
    }
    
    /// 停止回合计时器
    private func stopTurnTimer() {
        gameTimer?.invalidate()
        gameTimer = nil
    }
    
    /// 更新计时器
    private func updateTimer() {
        guard gameState.phase == .playing else {
            stopTurnTimer()
            return
        }
        
        gameState.remainingTime -= 1
        
        // 时间到，自动过牌或出最小牌
        if gameState.remainingTime <= 0 {
            handleTimeOut()
        }
    }
    
    /// 处理超时
    private func handleTimeOut() {
        guard let currentPlayer = gameState.currentPlayer else { return }
        
        if currentPlayer.type == .human {
            // 人类玩家超时，自动过牌
            _ = passCards()
        } else {
            // AI玩家超时，执行决策
            executeAITurn()
        }
    }
    
    // MARK: - 错误处理
    
    private func setError(_ message: String) {
        errorMessage = message
        
        // 3秒后自动清除错误信息
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) { [weak self] in
            if self?.errorMessage == message {
                self?.errorMessage = nil
            }
        }
    }
    
    private func clearError() {
        errorMessage = nil
    }
    
    // MARK: - 游戏信息
    
    /// 获取当前玩家可出的牌型
    func getValidCombinations() -> [CardCombination] {
        guard let currentPlayer = gameState.currentPlayer,
              currentPlayer.type == .human else {
            return []
        }
        
        return ruleValidator.getValidCombinations(
            for: currentPlayer,
            gameState: gameState
        )
    }
    
    /// 检查指定牌组是否可以出
    func canPlayCards(_ cards: [Card]) -> Bool {
        guard let currentPlayer = gameState.currentPlayer,
              currentPlayer.type == .human else {
            return false
        }
        
        let combination = CardCombination(cards: cards)
        return ruleValidator.validatePlay(combination, gameState: gameState)
    }
    
    /// 获取游戏统计信息
    func getGameStats() -> [String: Any] {
        return [
            "gameId": gameState.gameId.uuidString,
            "roundNumber": gameState.roundNumber,
            "totalPlayers": gameState.players.count,
            "currentPlayer": gameState.currentPlayer?.name ?? "无",
            "gamePhase": gameState.phase.rawValue,
            "playHistory": gameState.gameHistory.count
        ]
    }
    
    // MARK: - 清理
    deinit {
        stopTurnTimer()
        cancellables.removeAll()
    }
}
