//
//  Card.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import Foundation

/// 扑克牌花色枚举
enum CardSuit: String, CaseIterable, Codable {
    case spades = "♠"    // 黑桃
    case hearts = "♥"    // 红桃
    case diamonds = "♦"  // 方块
    case clubs = "♣"     // 梅花
    
    /// 花色权重（用于比较大小）
    var weight: Int {
        switch self {
        case .spades: return 4    // 黑桃最大
        case .hearts: return 3    // 红桃
        case .diamonds: return 2  // 方块
        case .clubs: return 1     // 梅花最小
        }
    }
    
    /// 花色颜色（红色或黑色）
    var isRed: Bool {
        return self == .hearts || self == .diamonds
    }
}

/// 扑克牌点数枚举
enum CardRank: Int, CaseIterable, Codable {
    case three = 3, four, five, six, seven, eight, nine, ten
    case jack = 11, queen, king, ace, two = 15
    
    /// 点数显示名称
    var displayName: String {
        switch self {
        case .three: return "3"
        case .four: return "4"
        case .five: return "5"
        case .six: return "6"
        case .seven: return "7"
        case .eight: return "8"
        case .nine: return "9"
        case .ten: return "10"
        case .jack: return "J"
        case .queen: return "Q"
        case .king: return "K"
        case .ace: return "A"
        case .two: return "2"
        }
    }
    
    /// 点数权重（用于比较大小，2最大）
    var weight: Int {
        return self.rawValue
    }
}

/// 扑克牌结构体
struct Card: Identifiable, Codable, Equatable, Hashable {
    let id = UUID()
    let suit: CardSuit
    let rank: CardRank
    
    /// 初始化扑克牌
    init(suit: CardSuit, rank: CardRank) {
        self.suit = suit
        self.rank = rank
    }
    
    /// 扑克牌显示名称
    var displayName: String {
        return "\(suit.rawValue)\(rank.displayName)"
    }
    
    /// 扑克牌权重（用于排序和比较）
    var weight: Int {
        return rank.weight * 10 + suit.weight
    }
    
    /// 是否为黑桃3（游戏开始必须出的牌）
    var isSpadeThree: Bool {
        return suit == .spades && rank == .three
    }
    
    /// 比较两张牌的大小
    static func < (lhs: Card, rhs: Card) -> Bool {
        if lhs.rank.weight != rhs.rank.weight {
            return lhs.rank.weight < rhs.rank.weight
        }
        return lhs.suit.weight < rhs.suit.weight
    }
    
    /// 创建一副完整的扑克牌（52张，不含大小王）
    static func createDeck() -> [Card] {
        var deck: [Card] = []
        for suit in CardSuit.allCases {
            for rank in CardRank.allCases {
                deck.append(Card(suit: suit, rank: rank))
            }
        }
        return deck
    }
    
    /// 洗牌
    static func shuffleDeck(_ deck: [Card]) -> [Card] {
        return deck.shuffled()
    }
}

// MARK: - 扑克牌组合类型
/// 出牌组合类型
enum CardCombinationType: String, CaseIterable {
    case single = "单张"           // 单张牌
    case pair = "对子"             // 一对
    case triple = "三张"           // 三张相同
    case straight = "顺子"         // 顺子（5张以上连续）
    case pairStraight = "连对"     // 连对（3对以上连续）
    case tripleStraight = "飞机"   // 飞机（2个以上连续三张）
    case bomb = "炸弹"             // 炸弹（四张相同）
    case invalid = "无效"          // 无效组合
    
    /// 组合类型权重（用于比较大小）
    var weight: Int {
        switch self {
        case .single: return 1
        case .pair: return 2
        case .triple: return 3
        case .straight: return 4
        case .pairStraight: return 5
        case .tripleStraight: return 6
        case .bomb: return 10  // 炸弹最大
        case .invalid: return 0
        }
    }
}

/// 扑克牌组合结构体
struct CardCombination: Equatable {
    let cards: [Card]
    let type: CardCombinationType
    let mainRank: CardRank?  // 主要点数（用于比较大小）
    
    init(cards: [Card]) {
        self.cards = cards.sorted()
        let (type, mainRank) = CardCombination.analyzeCards(cards)
        self.type = type
        self.mainRank = mainRank
    }
    
    /// 分析牌型
    private static func analyzeCards(_ cards: [Card]) -> (CardCombinationType, CardRank?) {
        let sortedCards = cards.sorted()
        let count = cards.count
        
        guard count > 0 else { return (.invalid, nil) }
        
        // 统计每个点数的数量
        let rankCounts = Dictionary(grouping: sortedCards, by: { $0.rank })
            .mapValues { $0.count }
        
        let counts = Array(rankCounts.values).sorted(by: >)
        let ranks = rankCounts.keys.sorted(by: { $0.weight < $1.weight })
        
        switch count {
        case 1:
            return (.single, sortedCards.first?.rank)
            
        case 2:
            if counts == [2] {
                return (.pair, sortedCards.first?.rank)
            }
            
        case 3:
            if counts == [3] {
                return (.triple, sortedCards.first?.rank)
            }
            
        case 4:
            if counts == [4] {
                return (.bomb, sortedCards.first?.rank)
            }
            
        default:
            // 检查顺子
            if count >= 5 && counts.allSatisfy({ $0 == 1 }) {
                if isConsecutive(ranks) {
                    return (.straight, ranks.first)
                }
            }
            
            // 检查连对
            if count >= 6 && count % 2 == 0 && counts.allSatisfy({ $0 == 2 }) {
                if isConsecutive(ranks) {
                    return (.pairStraight, ranks.first)
                }
            }
            
            // 检查飞机
            if count >= 6 && count % 3 == 0 && counts.allSatisfy({ $0 == 3 }) {
                if isConsecutive(ranks) {
                    return (.tripleStraight, ranks.first)
                }
            }
        }
        
        return (.invalid, nil)
    }
    
    /// 检查点数是否连续
    private static func isConsecutive(_ ranks: [CardRank]) -> Bool {
        guard ranks.count > 1 else { return false }
        
        for i in 1..<ranks.count {
            if ranks[i].weight != ranks[i-1].weight + 1 {
                return false
            }
        }
        return true
    }
    
    /// 比较两个组合的大小
    static func canBeat(_ challenger: CardCombination, _ current: CardCombination) -> Bool {
        // 炸弹可以打任何非炸弹牌型
        if challenger.type == .bomb && current.type != .bomb {
            return true
        }
        
        // 非炸弹不能打炸弹
        if challenger.type != .bomb && current.type == .bomb {
            return false
        }
        
        // 相同牌型比较
        if challenger.type == current.type && challenger.cards.count == current.cards.count {
            guard let challengerRank = challenger.mainRank,
                  let currentRank = current.mainRank else {
                return false
            }
            return challengerRank.weight > currentRank.weight
        }
        
        return false
    }
    
    /// 是否为有效组合
    var isValid: Bool {
        return type != .invalid
    }
}
