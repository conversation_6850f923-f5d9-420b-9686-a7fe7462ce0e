//
//  CardRecorderView.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import SwiftUI

/// 记牌器主界面
struct CardRecorderView: View {
    @StateObject private var recognitionService = CardRecognitionService.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedImage: UIImage?
    @State private var showImagePicker = false
    @State private var showCamera = false
    @State private var showPhotoLibrary = false
    @State private var showGuide = false
    @State private var showHistory = false
    @State private var currentTab = 0
    
    var body: some View {
        NavigationView {
            TabView(selection: $currentTab) {
                // 识别页面
                RecognitionTab(
                    recognitionService: recognitionService,
                    selectedImage: $selectedImage,
                    showImagePicker: $showImagePicker,
                    showCamera: $showCamera,
                    showPhotoLibrary: $showPhotoLibrary,
                    showGuide: $showGuide
                )
                .tabItem {
                    Image(systemName: "camera.viewfinder")
                    Text("识别")
                }
                .tag(0)
                
                // 历史记录页面
                HistoryTab(recognitionService: recognitionService)
                    .tabItem {
                        Image(systemName: "clock.fill")
                        Text("历史")
                    }
                    .tag(1)
                
                // 统计页面
                StatsTab(recognitionService: recognitionService)
                    .tabItem {
                        Image(systemName: "chart.bar.fill")
                        Text("统计")
                    }
                    .tag(2)
            }
            .navigationTitle("扑克牌记牌器")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showGuide = true }) {
                        Image(systemName: "questionmark.circle")
                    }
                }
            }
        }
        .sheet(isPresented: $showImagePicker) {
            ImageSourcePicker(
                showCamera: $showCamera,
                showPhotoLibrary: $showPhotoLibrary,
                isPresented: $showImagePicker
            )
        }
        .sheet(isPresented: $showCamera) {
            ImagePicker(
                selectedImage: $selectedImage,
                isPresented: $showCamera,
                sourceType: .camera
            )
        }
        .sheet(isPresented: $showPhotoLibrary) {
            ImagePicker(
                selectedImage: $selectedImage,
                isPresented: $showPhotoLibrary,
                sourceType: .photoLibrary
            )
        }
        .sheet(isPresented: $showGuide) {
            CameraGuideView(isPresented: $showGuide)
        }
        .onChange(of: selectedImage) { image in
            if let image = image {
                recognizeCards(from: image)
            }
        }
    }
    
    private func recognizeCards(from image: UIImage) {
        recognitionService.recognizeCards(from: image) { result in
            switch result {
            case .success(let recognitionResult):
                recognitionService.saveRecognitionHistory(recognitionResult)
            case .failure(let error):
                print("识别失败: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - 识别页面
struct RecognitionTab: View {
    @ObservedObject var recognitionService: CardRecognitionService
    @Binding var selectedImage: UIImage?
    @Binding var showImagePicker: Bool
    @Binding var showCamera: Bool
    @Binding var showPhotoLibrary: Bool
    @Binding var showGuide: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            if recognitionService.isProcessing {
                // 处理中状态
                ProcessingView()
            } else if let result = recognitionService.recognitionResult {
                // 显示识别结果
                RecognitionResultView(result: result)
            } else {
                // 初始状态
                InitialStateView(
                    showImagePicker: $showImagePicker,
                    showGuide: $showGuide
                )
            }
            
            Spacer()
            
            // 底部按钮
            BottomActionButtons(
                showImagePicker: $showImagePicker,
                onSimulate: {
                    recognitionService.simulateRecognition { _ in }
                }
            )
        }
        .padding()
    }
}

// MARK: - 历史记录页面
struct HistoryTab: View {
    @ObservedObject var recognitionService: CardRecognitionService
    @State private var showClearAlert = false
    
    var body: some View {
        VStack {
            let history = recognitionService.getRecognitionHistory()
            
            if history.isEmpty {
                // 空状态
                VStack(spacing: 20) {
                    Image(systemName: "clock.badge.xmark")
                        .font(.system(size: 60))
                        .foregroundColor(.gray)
                    
                    Text("暂无识别记录")
                        .font(.headline)
                        .foregroundColor(.gray)
                    
                    Text("开始拍照识别扑克牌吧！")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 历史记录列表
                List {
                    ForEach(Array(history.enumerated().reversed()), id: \.offset) { index, result in
                        HistoryRowView(result: result, index: history.count - index)
                    }
                }
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("清除") {
                            showClearAlert = true
                        }
                        .foregroundColor(.red)
                    }
                }
            }
        }
        .alert("清除历史记录", isPresented: $showClearAlert) {
            Button("取消", role: .cancel) {}
            Button("清除", role: .destructive) {
                recognitionService.clearRecognitionHistory()
            }
        } message: {
            Text("确定要清除所有识别历史记录吗？")
        }
    }
}

// MARK: - 统计页面
struct StatsTab: View {
    @ObservedObject var recognitionService: CardRecognitionService
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                let stats = recognitionService.getRecognitionStats()
                
                // 总体统计
                StatsCardView(
                    title: "总识别次数",
                    value: "\(stats.totalRecognitions)",
                    icon: "camera.fill",
                    color: .blue
                )
                
                StatsCardView(
                    title: "识别扑克牌总数",
                    value: "\(stats.totalCardsRecognized)",
                    icon: "suit.club.fill",
                    color: .green
                )
                
                StatsCardView(
                    title: "平均置信度",
                    value: "\(stats.averageConfidencePercentage)%",
                    icon: "checkmark.seal.fill",
                    color: .orange
                )
                
                StatsCardView(
                    title: "平均处理时间",
                    value: stats.formattedAverageProcessingTime,
                    icon: "timer",
                    color: .purple
                )
                
                // 使用提示
                VStack(alignment: .leading, spacing: 10) {
                    Text("使用提示")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        TipRow(icon: "lightbulb.fill", text: "确保光线充足，避免阴影")
                        TipRow(icon: "camera.fill", text: "保持手机稳定，避免模糊")
                        TipRow(icon: "rectangle.fill", text: "扑克牌平铺，避免重叠")
                        TipRow(icon: "eye.fill", text: "确保所有牌面清晰可见")
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
            }
            .padding()
        }
    }
}

// MARK: - 子组件

struct ProcessingView: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(spacing: 20) {
            ZStack {
                Circle()
                    .stroke(Color.blue.opacity(0.3), lineWidth: 4)
                    .frame(width: 80, height: 80)
                
                Circle()
                    .trim(from: 0, to: 0.7)
                    .stroke(Color.blue, lineWidth: 4)
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(isAnimating ? 360 : 0))
                    .animation(
                        Animation.linear(duration: 1.0).repeatForever(autoreverses: false),
                        value: isAnimating
                    )
            }
            
            Text("正在识别扑克牌...")
                .font(.headline)
                .foregroundColor(.blue)
        }
        .onAppear {
            isAnimating = true
        }
    }
}

struct RecognitionResultView: View {
    let result: CardRecognitionResult
    
    var body: some View {
        VStack(spacing: 20) {
            // 成功图标
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)
            
            // 识别结果
            VStack(spacing: 10) {
                Text("识别成功！")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("识别到 \(result.cardCount) 张扑克牌")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // 结果展示
            VStack(spacing: 15) {
                Text("识别结果：")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(result.formattedString)
                    .font(.title3)
                    .fontWeight(.medium)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)
                    .textSelection(.enabled)
                
                // 详细信息
                HStack(spacing: 30) {
                    VStack {
                        Text("置信度")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(result.confidencePercentage)%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                    
                    VStack {
                        Text("处理时间")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(result.formattedProcessingTime)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                }
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
    }
}

struct InitialStateView: View {
    @Binding var showImagePicker: Bool
    @Binding var showGuide: Bool
    
    var body: some View {
        VStack(spacing: 30) {
            // 图标
            Image(systemName: "camera.viewfinder")
                .font(.system(size: 80))
                .foregroundColor(.blue)
            
            // 说明文字
            VStack(spacing: 10) {
                Text("扑克牌识别")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("拍摄或选择包含扑克牌的照片\n自动识别并转换为标准格式")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 功能特点
            VStack(alignment: .leading, spacing: 12) {
                FeatureRow(icon: "eye.fill", text: "智能识别扑克牌数值")
                FeatureRow(icon: "arrow.up.arrow.down", text: "自动按大小排序")
                FeatureRow(icon: "doc.on.clipboard", text: "一键复制结果")
                FeatureRow(icon: "clock.fill", text: "保存识别历史")
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
    }
}

struct BottomActionButtons: View {
    @Binding var showImagePicker: Bool
    let onSimulate: () -> Void
    
    var body: some View {
        VStack(spacing: 15) {
            // 主要按钮
            Button(action: { showImagePicker = true }) {
                HStack {
                    Image(systemName: "camera.fill")
                        .font(.title3)
                    Text("开始识别")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(12)
            }
            
            // 演示按钮
            Button(action: onSimulate) {
                HStack {
                    Image(systemName: "play.circle")
                        .font(.title3)
                    Text("演示识别")
                        .font(.subheadline)
                }
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
}

struct HistoryRowView: View {
    let result: CardRecognitionResult
    let index: Int
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("第 \(index) 次识别")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text(result.timestamp, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(result.formattedString)
                .font(.body)
                .padding(.vertical, 4)
                .padding(.horizontal, 8)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(6)
            
            HStack {
                Label("\(result.cardCount)张", systemImage: "suit.club.fill")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Label("\(result.confidencePercentage)%", systemImage: "checkmark.seal")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

struct StatsCardView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
}

struct FeatureRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

struct TipRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 10) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.orange)
                .frame(width: 16)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
}

#Preview {
    CardRecorderView()
}
