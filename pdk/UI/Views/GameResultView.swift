//
//  GameResultView.swift
//  pdk
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/21.
//

import SwiftUI

/// 游戏结果界面
struct GameResultView: View {
    @ObservedObject var viewModel: GameViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ZStack {
            // 背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.purple.opacity(0.8),
                    Color.blue.opacity(0.6)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 30) {
                Spacer()
                
                // 结果标题
                VStack(spacing: 15) {
                    // 胜负图标
                    if isPlayerWinner {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.yellow)
                            .shadow(color: .orange, radius: 10)
                    } else {
                        Image(systemName: "hand.thumbsup.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                    }
                    
                    // 结果文字
                    Text(viewModel.getGameResultDescription())
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                }
                
                // 游戏统计
                GameStatsView(viewModel: viewModel)
                
                // 玩家排名
                PlayerRankingView(viewModel: viewModel)
                
                Spacer()
                
                // 按钮组
                VStack(spacing: 15) {
                    // 再来一局
                    Button(action: {
                        viewModel.startNewGame()
                        dismiss()
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                                .font(.title3)
                            Text("再来一局")
                                .font(.title3)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green)
                                .shadow(color: .black.opacity(0.2), radius: 5)
                        )
                    }
                    
                    // 返回大厅
                    Button(action: {
                        dismiss()
                    }) {
                        HStack {
                            Image(systemName: "house.fill")
                                .font(.title3)
                            Text("返回大厅")
                                .font(.title3)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue)
                                .shadow(color: .black.opacity(0.2), radius: 5)
                        )
                    }
                }
                .padding(.horizontal, 40)
                
                Spacer()
            }
            .padding()
        }
    }
    
    // MARK: - 计算属性
    
    private var isPlayerWinner: Bool {
        guard let result = viewModel.gameState.result,
              let humanPlayer = viewModel.humanPlayer else {
            return false
        }
        
        return result.winners.first == humanPlayer.id
    }
}

// MARK: - 游戏统计视图
struct GameStatsView: View {
    @ObservedObject var viewModel: GameViewModel
    
    var body: some View {
        VStack(spacing: 15) {
            Text("游戏统计")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            HStack(spacing: 30) {
                StatItem(
                    title: "游戏时长",
                    value: formatDuration(viewModel.gameState.result?.duration ?? 0),
                    icon: "clock.fill"
                )
                
                StatItem(
                    title: "总回合",
                    value: "\(viewModel.gameState.result?.totalRounds ?? 0)",
                    icon: "arrow.triangle.2.circlepath"
                )
                
                StatItem(
                    title: "得分",
                    value: "\(getPlayerScore())",
                    icon: "star.fill"
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.white.opacity(0.2))
                .backdrop(BlurView(style: .systemMaterial))
        )
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    private func getPlayerScore() -> Int {
        guard let result = viewModel.gameState.result,
              let humanPlayer = viewModel.humanPlayer else {
            return 0
        }
        
        return result.scores[humanPlayer.id] ?? 0
    }
}

// MARK: - 统计项组件
struct StatItem: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.yellow)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
    }
}

// MARK: - 玩家排名视图
struct PlayerRankingView: View {
    @ObservedObject var viewModel: GameViewModel
    
    var body: some View {
        VStack(spacing: 15) {
            Text("最终排名")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            VStack(spacing: 10) {
                ForEach(Array(rankedPlayers.enumerated()), id: \.offset) { index, player in
                    PlayerRankRow(
                        rank: index + 1,
                        player: player,
                        score: getScore(for: player),
                        isHuman: player.type == .human
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.white.opacity(0.2))
                .backdrop(BlurView(style: .systemMaterial))
        )
    }
    
    private var rankedPlayers: [Player] {
        guard let result = viewModel.gameState.result else {
            return viewModel.gameState.players.sorted { $0.cardCount < $1.cardCount }
        }
        
        return result.winners.compactMap { winnerId in
            viewModel.gameState.players.first { $0.id == winnerId }
        }
    }
    
    private func getScore(for player: Player) -> Int {
        guard let result = viewModel.gameState.result else { return 0 }
        return result.scores[player.id] ?? 0
    }
}

// MARK: - 玩家排名行
struct PlayerRankRow: View {
    let rank: Int
    let player: Player
    let score: Int
    let isHuman: Bool
    
    var body: some View {
        HStack(spacing: 15) {
            // 排名
            ZStack {
                Circle()
                    .fill(rankColor)
                    .frame(width: 30, height: 30)
                
                Text("\(rank)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            // 玩家信息
            HStack(spacing: 10) {
                // 头像
                Circle()
                    .fill(Color.blue.opacity(0.7))
                    .frame(width: 35, height: 35)
                    .overlay {
                        Text(String(player.name.prefix(1)))
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(isHuman ? "您" : player.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text("剩余 \(player.cardCount) 张牌")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            
            Spacer()
            
            // 得分
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(score)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.yellow)
                
                Text("分")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.horizontal, 15)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(isHuman ? Color.yellow.opacity(0.2) : Color.white.opacity(0.1))
        )
    }
    
    private var rankColor: Color {
        switch rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .blue
        }
    }
}

// MARK: - 加载界面
struct LoadingView: View {
    @State private var isAnimating = false
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.6)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                // 加载动画
                ZStack {
                    Circle()
                        .stroke(Color.white.opacity(0.3), lineWidth: 4)
                        .frame(width: 60, height: 60)
                    
                    Circle()
                        .trim(from: 0, to: 0.7)
                        .stroke(Color.white, lineWidth: 4)
                        .frame(width: 60, height: 60)
                        .rotationEffect(.degrees(isAnimating ? 360 : 0))
                        .animation(
                            Animation.linear(duration: 1.0).repeatForever(autoreverses: false),
                            value: isAnimating
                        )
                }
                
                Text("游戏准备中...")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            .padding(40)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.8))
                    .backdrop(BlurView(style: .systemMaterial))
            )
        }
        .onAppear {
            isAnimating = true
        }
    }
}

#Preview("游戏结果") {
    GameResultView(viewModel: GameViewModel())
}

#Preview("加载界面") {
    LoadingView()
}
